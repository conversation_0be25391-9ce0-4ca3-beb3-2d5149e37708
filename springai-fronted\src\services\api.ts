import type { 
  ApiResponse, 
  Program, 
  Category, 
  PaginationData, 
  ProgramQueryParams 
} from '@/types'

// API 基础配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080'

// 通用请求函数
async function request<T>(
  endpoint: string, 
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  const url = `${API_BASE_URL}${endpoint}`
  
  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  }

  try {
    const response = await fetch(url, defaultOptions)
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    return data
  } catch (error) {
    console.error('API request failed:', error)
    throw error
  }
}

// 节目相关 API
export const programApi = {
  // 获取节目列表
  async getPrograms(params: ProgramQueryParams = {}): Promise<ApiResponse<PaginationData<Program>>> {
    const searchParams = new URLSearchParams()
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value))
      }
    })
    
    const queryString = searchParams.toString()
    const endpoint = `/api/programs${queryString ? `?${queryString}` : ''}`
    
    return request<PaginationData<Program>>(endpoint)
  },

  // 获取节目详情
  async getProgramDetail(programId: number): Promise<ApiResponse<Program>> {
    return request<Program>(`/api/programs/${programId}`)
  },

  // 播放节目（统计播放次数）
  async playProgram(programId: number): Promise<ApiResponse<null>> {
    return request<null>(`/api/programs/${programId}/play`, {
      method: 'POST'
    })
  },

  // 获取热门节目
  async getHotPrograms(page = 1, limit = 10): Promise<ApiResponse<PaginationData<Program>>> {
    return request<PaginationData<Program>>(`/api/programs/hot?page=${page}&limit=${limit}`)
  },

  // 获取精选节目
  async getFeaturedPrograms(page = 1, limit = 10): Promise<ApiResponse<PaginationData<Program>>> {
    return request<PaginationData<Program>>(`/api/programs/featured?page=${page}&limit=${limit}`)
  }
}

// 分类相关 API
export const categoryApi = {
  // 获取所有分类
  async getCategories(): Promise<ApiResponse<Category[]>> {
    return request<Category[]>('/api/categories')
  },

  // 获取分类详情
  async getCategoryDetail(categoryId: number): Promise<ApiResponse<Category>> {
    return request<Category>(`/api/categories/${categoryId}`)
  },

  // 创建分类
  async createCategory(data: { name: string; description: string }): Promise<ApiResponse<Category>> {
    return request<Category>('/api/categories', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }
}

// 工具函数
export const utils = {
  // 格式化时长（秒转为 mm:ss 格式）
  formatDuration(seconds: number): string {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
  },

  // 格式化播放次数
  formatPlayCount(count: number): string {
    if (count >= 10000) {
      return `${(count / 10000).toFixed(1)}万`
    }
    return count.toString()
  },

  // 格式化日期
  formatDate(dateString: string): string {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }
}
