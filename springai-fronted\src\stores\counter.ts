import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import type { Program, Category, PlayerState } from '@/types'
import { programApi, categoryApi } from '@/services/api'

// 节目状态管理
export const useProgramStore = defineStore('program', () => {
  const programs = ref<Program[]>([])
  const featuredPrograms = ref<Program[]>([])
  const hotPrograms = ref<Program[]>([])
  const currentProgram = ref<Program | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 分页信息
  const pagination = ref({
    current: 1,
    size: 10,
    total: 0,
    pages: 0,
    hasNext: false,
    hasPrevious: false
  })

  // 获取节目列表
  const fetchPrograms = async (params: any = {}) => {
    loading.value = true
    error.value = null

    try {
      const response = await programApi.getPrograms(params)
      if (response.success) {
        programs.value = response.data.records
        pagination.value = {
          current: response.data.current,
          size: response.data.size,
          total: response.data.total,
          pages: response.data.pages,
          hasNext: response.data.hasNext,
          hasPrevious: response.data.hasPrevious
        }
      }
    } catch (err) {
      error.value = '获取节目列表失败'
      console.error(err)
    } finally {
      loading.value = false
    }
  }

  // 获取精选节目
  const fetchFeaturedPrograms = async (limit = 5) => {
    try {
      const response = await programApi.getFeaturedPrograms(1, limit)
      if (response.success) {
        featuredPrograms.value = response.data.records
      }
    } catch (err) {
      console.error('获取精选节目失败:', err)
    }
  }

  // 获取热门节目
  const fetchHotPrograms = async (limit = 10) => {
    try {
      const response = await programApi.getHotPrograms(1, limit)
      if (response.success) {
        hotPrograms.value = response.data.records
      }
    } catch (err) {
      console.error('获取热门节目失败:', err)
    }
  }

  // 获取节目详情
  const fetchProgramDetail = async (id: number) => {
    loading.value = true
    error.value = null

    try {
      const response = await programApi.getProgramDetail(id)
      if (response.success) {
        currentProgram.value = response.data
        return response.data
      }
    } catch (err) {
      error.value = '获取节目详情失败'
      console.error(err)
    } finally {
      loading.value = false
    }
  }

  return {
    programs,
    featuredPrograms,
    hotPrograms,
    currentProgram,
    loading,
    error,
    pagination,
    fetchPrograms,
    fetchFeaturedPrograms,
    fetchHotPrograms,
    fetchProgramDetail
  }
})

// 分类状态管理
export const useCategoryStore = defineStore('category', () => {
  const categories = ref<Category[]>([])
  const currentCategory = ref<Category | null>(null)
  const loading = ref(false)

  // 获取所有分类
  const fetchCategories = async () => {
    loading.value = true

    try {
      const response = await categoryApi.getCategories()
      if (response.success) {
        categories.value = response.data
      }
    } catch (err) {
      console.error('获取分类列表失败:', err)
    } finally {
      loading.value = false
    }
  }

  return {
    categories,
    currentCategory,
    loading,
    fetchCategories
  }
})

// 播放器状态管理
export const usePlayerStore = defineStore('player', () => {
  const playerState = ref<PlayerState>({
    currentProgram: null,
    isPlaying: false,
    currentTime: 0,
    duration: 0,
    volume: 1,
    isLoading: false
  })

  // 播放节目
  const playProgram = async (program: Program) => {
    playerState.value.currentProgram = program
    playerState.value.isLoading = true

    try {
      // 调用播放统计 API
      await programApi.playProgram(program.id)
      playerState.value.isPlaying = true
    } catch (err) {
      console.error('播放失败:', err)
    } finally {
      playerState.value.isLoading = false
    }
  }

  // 暂停播放
  const pauseProgram = () => {
    playerState.value.isPlaying = false
  }

  // 继续播放
  const resumeProgram = () => {
    playerState.value.isPlaying = true
  }

  // 停止播放
  const stopProgram = () => {
    playerState.value.isPlaying = false
    playerState.value.currentTime = 0
  }

  // 设置播放时间
  const setCurrentTime = (time: number) => {
    playerState.value.currentTime = time
  }

  // 设置总时长
  const setDuration = (duration: number) => {
    playerState.value.duration = duration
  }

  // 设置音量
  const setVolume = (volume: number) => {
    playerState.value.volume = Math.max(0, Math.min(1, volume))
  }

  return {
    playerState: computed(() => playerState.value),
    playProgram,
    pauseProgram,
    resumeProgram,
    stopProgram,
    setCurrentTime,
    setDuration,
    setVolume
  }
})
